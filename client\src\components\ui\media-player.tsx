import React, { useState, useRef, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Slider } from '@/components/ui/slider';
import { cn } from '@/lib/utils';
import { 
  Play, 
  Pause, 
  Volume2, 
  VolumeX, 
  SkipBack, 
  SkipForward,
  ExternalLink,
  Maximize2,
  Minimize2
} from 'lucide-react';

interface MediaPlayerProps {
  src: string;
  type: 'audio' | 'video';
  title?: string;
  className?: string;
  onPopOut?: () => void;
  isPoppedOut?: boolean;
  autoPlay?: boolean;
  controls?: boolean;
}

export function MediaPlayer({
  src,
  type,
  title,
  className,
  onPopOut,
  isPoppedOut = false,
  autoPlay = false,
  controls = true,
}: MediaPlayerProps) {
  const mediaRef = useRef<HTMLVideoElement | HTMLAudioElement>(null);
  const [isPlaying, setIsPlaying] = useState(false);
  const [currentTime, setCurrentTime] = useState(0);
  const [duration, setDuration] = useState(0);
  const [volume, setVolume] = useState(1);
  const [isMuted, setIsMuted] = useState(false);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    const media = mediaRef.current;
    if (!media) return;

    const updateTime = () => setCurrentTime(media.currentTime);
    const updateDuration = () => {
      setDuration(media.duration);
      setIsLoading(false);
    };
    const handlePlay = () => setIsPlaying(true);
    const handlePause = () => setIsPlaying(false);
    const handleLoadStart = () => setIsLoading(true);
    const handleCanPlay = () => setIsLoading(false);

    media.addEventListener('timeupdate', updateTime);
    media.addEventListener('loadedmetadata', updateDuration);
    media.addEventListener('play', handlePlay);
    media.addEventListener('pause', handlePause);
    media.addEventListener('loadstart', handleLoadStart);
    media.addEventListener('canplay', handleCanPlay);

    return () => {
      media.removeEventListener('timeupdate', updateTime);
      media.removeEventListener('loadedmetadata', updateDuration);
      media.removeEventListener('play', handlePlay);
      media.removeEventListener('pause', handlePause);
      media.removeEventListener('loadstart', handleLoadStart);
      media.removeEventListener('canplay', handleCanPlay);
    };
  }, []);

  const togglePlayPause = () => {
    const media = mediaRef.current;
    if (!media) return;

    if (isPlaying) {
      media.pause();
    } else {
      media.play();
    }
  };

  const handleSeek = (value: number[]) => {
    const media = mediaRef.current;
    if (!media) return;

    const newTime = value[0];
    media.currentTime = newTime;
    setCurrentTime(newTime);
  };

  const handleVolumeChange = (value: number[]) => {
    const media = mediaRef.current;
    if (!media) return;

    const newVolume = value[0];
    media.volume = newVolume;
    setVolume(newVolume);
    setIsMuted(newVolume === 0);
  };

  const toggleMute = () => {
    const media = mediaRef.current;
    if (!media) return;

    if (isMuted) {
      media.volume = volume > 0 ? volume : 0.5;
      setIsMuted(false);
    } else {
      media.volume = 0;
      setIsMuted(true);
    }
  };

  const skipBackward = () => {
    const media = mediaRef.current;
    if (!media) return;

    media.currentTime = Math.max(0, media.currentTime - 10);
  };

  const skipForward = () => {
    const media = mediaRef.current;
    if (!media) return;

    media.currentTime = Math.min(duration, media.currentTime + 10);
  };

  const formatTime = (time: number) => {
    if (isNaN(time)) return '0:00';
    
    const minutes = Math.floor(time / 60);
    const seconds = Math.floor(time % 60);
    return `${minutes}:${seconds.toString().padStart(2, '0')}`;
  };

  return (
    <div className={cn('bg-background border rounded-lg overflow-hidden', className)}>
      {/* Media Element */}
      <div className="relative">
        {type === 'video' ? (
          <video
            ref={mediaRef as React.RefObject<HTMLVideoElement>}
            src={src}
            className="w-full h-auto bg-black"
            autoPlay={autoPlay}
            preload="metadata"
          >
            Your browser does not support the video tag.
          </video>
        ) : (
          <div className="bg-gradient-to-br from-primary/10 to-primary/5 p-8 flex items-center justify-center min-h-[200px]">
            <div className="text-center">
              <div className="w-16 h-16 mx-auto mb-4 bg-primary/20 rounded-full flex items-center justify-center">
                <i className="ri-music-line text-2xl text-primary"></i>
              </div>
              <h3 className="font-medium text-foreground">{title || 'Audio File'}</h3>
              <p className="text-sm text-muted-foreground mt-1">
                {formatTime(duration)}
              </p>
            </div>
            <audio
              ref={mediaRef as React.RefObject<HTMLAudioElement>}
              src={src}
              autoPlay={autoPlay}
              preload="metadata"
            >
              Your browser does not support the audio tag.
            </audio>
          </div>
        )}

        {/* Loading overlay */}
        {isLoading && (
          <div className="absolute inset-0 bg-black/50 flex items-center justify-center">
            <div className="text-white text-center">
              <i className="ri-loader-4-line animate-spin text-2xl mb-2"></i>
              <p className="text-sm">Loading...</p>
            </div>
          </div>
        )}
      </div>

      {/* Controls */}
      {controls && (
        <div className="p-4 bg-background border-t">
          {/* Title and popout button */}
          <div className="flex items-center justify-between mb-3">
            <h4 className="font-medium text-sm text-foreground truncate">
              {title || `${type === 'video' ? 'Video' : 'Audio'} File`}
            </h4>
            {onPopOut && (
              <Button
                variant="ghost"
                size="sm"
                className="h-7 w-7 p-0"
                title={isPoppedOut ? "Pop in player" : "Pop out player"}
                onClick={onPopOut}
              >
                {isPoppedOut ? (
                  <Minimize2 className="h-3 w-3" />
                ) : (
                  <ExternalLink className="h-3 w-3" />
                )}
              </Button>
            )}
          </div>

          {/* Progress bar */}
          <div className="mb-3">
            <Slider
              value={[currentTime]}
              max={duration || 100}
              step={1}
              onValueChange={handleSeek}
              className="w-full"
              disabled={isLoading}
            />
            <div className="flex justify-between text-xs text-muted-foreground mt-1">
              <span>{formatTime(currentTime)}</span>
              <span>{formatTime(duration)}</span>
            </div>
          </div>

          {/* Control buttons */}
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <Button
                variant="ghost"
                size="sm"
                className="h-8 w-8 p-0"
                onClick={skipBackward}
                disabled={isLoading}
                title="Skip back 10s"
              >
                <SkipBack className="h-4 w-4" />
              </Button>
              
              <Button
                variant="ghost"
                size="sm"
                className="h-10 w-10 p-0"
                onClick={togglePlayPause}
                disabled={isLoading}
              >
                {isPlaying ? (
                  <Pause className="h-5 w-5" />
                ) : (
                  <Play className="h-5 w-5" />
                )}
              </Button>

              <Button
                variant="ghost"
                size="sm"
                className="h-8 w-8 p-0"
                onClick={skipForward}
                disabled={isLoading}
                title="Skip forward 10s"
              >
                <SkipForward className="h-4 w-4" />
              </Button>
            </div>

            {/* Volume controls */}
            <div className="flex items-center gap-2">
              <Button
                variant="ghost"
                size="sm"
                className="h-8 w-8 p-0"
                onClick={toggleMute}
                disabled={isLoading}
              >
                {isMuted ? (
                  <VolumeX className="h-4 w-4" />
                ) : (
                  <Volume2 className="h-4 w-4" />
                )}
              </Button>
              <Slider
                value={[isMuted ? 0 : volume]}
                max={1}
                step={0.1}
                onValueChange={handleVolumeChange}
                className="w-20"
                disabled={isLoading}
              />
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
