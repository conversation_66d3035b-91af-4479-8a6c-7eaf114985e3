import React, { useState, useRef } from 'react';
import { OutlineItem as OutlineItemType, Note } from '@/lib/types';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import {
  Collapsible,
  CollapsibleTrigger,
  CollapsibleContent,
} from '@/components/ui/collapsible';
import {
  DropdownMenu,
  DropdownMenuTrigger,
  DropdownMenuContent,
  DropdownMenuItem,
} from '@/components/ui/dropdown-menu';

interface SimpleOutlineItemProps {
  item: OutlineItemType;
  index: number;
  onUpdate: (item: OutlineItemType) => void;
  onAddChild: () => void;
  depth: number;
  allNotes: Note[];
  onAddNote: (linkedOutlineId: string, options?: { type?: 'text' | 'image' | 'video' | 'audio' | 'file' }) => Note;
  onLinkNoteToOutlineItem: (noteId: string, outlineItemId: string) => void;
  onDeleteOutlineItemWithNotes: (outlineItemId: string) => void;
  onNoteDelete: (noteId: string) => void;
  onNoteDuplicate: (noteId: string) => void;
  onNoteUpdate: (updatedNote: Note, forceSaveNow?: boolean) => void;
  onFileUpload: (file: File) => Promise<string>;
  allOutlineItems: OutlineItemType[];
  // Drag and drop props
  onDragStart?: (item: OutlineItemType) => void;
  onDragOver?: (targetId: string, position: 'before' | 'after' | 'child') => void;
  onDrop?: (draggedId: string, targetId: string, position: 'before' | 'after' | 'child') => void;
  onDragEnd?: () => void;
  isDraggedOver?: boolean;
  dragOverPosition?: 'before' | 'after' | 'child';
}

export function SimpleOutlineItem({
  item,
  index,
  onUpdate,
  onAddChild,
  depth = 0,
  allNotes,
  onAddNote,
  onLinkNoteToOutlineItem,
  onDeleteOutlineItemWithNotes,
  onNoteDelete,
  onNoteDuplicate,
  onNoteUpdate,
  onFileUpload,
  allOutlineItems,
  onDragStart,
  onDragOver,
  onDrop,
  onDragEnd,
  isDraggedOver = false,
  dragOverPosition,
}: SimpleOutlineItemProps) {
  const [isEditing, setIsEditing] = useState(false);
  const [isExpanded, setIsExpanded] = useState(true);
  const [isHovered, setIsHovered] = useState(false);
  const itemRef = useRef<HTMLDivElement>(null);

  const ownLinkedNotes = (allNotes || []).filter(note => note.linkedOutlineId === item.id);

  const handleTitleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    onUpdate({ ...item, title: e.target.value });
  };

  const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Enter') {
      setIsEditing(false);
    } else if (e.key === 'Escape') {
      setIsEditing(false);
    }
  };

  // Simple drag and drop handlers
  const handleDragStart = (e: React.DragEvent) => {
    e.dataTransfer.setData('text/plain', item.id);
    e.dataTransfer.effectAllowed = 'move';
    if (onDragStart) onDragStart(item);
  };

  const handleDragOver = (e: React.DragEvent, position: 'before' | 'after' | 'child') => {
    e.preventDefault();
    e.dataTransfer.dropEffect = 'move';
    if (onDragOver) onDragOver(item.id, position);
  };

  const handleDrop = (e: React.DragEvent, position: 'before' | 'after' | 'child') => {
    e.preventDefault();
    const draggedItemId = e.dataTransfer.getData('text/plain');
    if (onDrop && draggedItemId !== item.id) {
      onDrop(draggedItemId, item.id, position);
    }
  };

  const handleDragLeave = () => {
    // Clear drag over state when leaving
    if (onDragOver) onDragOver('', 'before');
  };

  return (
    <>
      {/* Before drop zone */}
      <div
        className={`h-2 -mt-1 rounded-full mb-1 mx-2 flex items-center justify-center transition-all ${
          isDraggedOver && dragOverPosition === 'before' 
            ? 'bg-blue-400 h-4' 
            : 'bg-transparent hover:bg-blue-100'
        }`}
        onDragOver={(e) => handleDragOver(e, 'before')}
        onDrop={(e) => handleDrop(e, 'before')}
        onDragLeave={handleDragLeave}
      />

      <div
        ref={itemRef}
        className={`outline-item group mb-0.5 py-1 px-1 transition-all max-w-full min-w-0 overflow-visible ${
          isDraggedOver && dragOverPosition === 'child' ? 'outline outline-2 outline-primary' : ''
        }`}
        onMouseEnter={() => setIsHovered(true)}
        onMouseLeave={() => setIsHovered(false)}
        style={{ marginLeft: `${depth * 20}px` }}
      >
        {/* Child drop zone */}
        <div
          className={`absolute left-0 top-0 w-8 h-full cursor-pointer transition-all ${
            isDraggedOver && dragOverPosition === 'child' 
              ? 'bg-blue-400 opacity-70 border-r-2 border-blue-600' 
              : 'opacity-0 hover:opacity-30 hover:bg-blue-200'
          }`}
          onDragOver={(e) => handleDragOver(e, 'child')}
          onDrop={(e) => handleDrop(e, 'child')}
          onDragLeave={handleDragLeave}
          title="Drop here to make this item a child"
          style={{ zIndex: 10 }}
        />

        <div className="flex items-start gap-2 w-full relative">
          {/* Drag handle */}
          <div
            className="drag-handle p-1 text-neutral-300 group-hover:text-neutral-500 cursor-grab active:cursor-grabbing hover:text-neutral-600 hover:bg-neutral-100 rounded transition-colors mt-0.5 relative flex-shrink-0"
            style={{ zIndex: 20 }}
            draggable
            onDragStart={handleDragStart}
            onDragEnd={onDragEnd}
            title="Drag to reorder"
          >
            <i className="ri-drag-move-fill text-sm"></i>
          </div>

          <div className="flex-1 min-w-0 max-w-[calc(100%-30px)] pr-2">
            <div className="flex items-center mb-0.5 gap-2 w-full min-w-0 pr-1 relative">
              <span className="outline-item-number font-mono text-neutral-500 min-w-fit pr-2 flex-shrink-0 text-xs">
                {item.number}
              </span>
              
              <div className="flex-1 min-w-0 overflow-hidden">
                <div className="flex items-center space-x-2">
                  {isEditing ? (
                    <Input
                      value={item.title}
                      onChange={handleTitleChange}
                      onBlur={() => setIsEditing(false)}
                      onKeyDown={handleKeyDown}
                      className="text-sm font-medium border-none shadow-none p-0 h-auto bg-transparent focus-visible:ring-0"
                      autoFocus
                    />
                  ) : (
                    <span
                      className="text-sm font-medium cursor-pointer hover:text-primary transition-colors truncate"
                      onClick={() => setIsEditing(true)}
                    >
                      {item.title}
                    </span>
                  )}
                </div>
              </div>

              {/* Action buttons */}
              {isHovered && (
                <div className="flex items-center gap-1 opacity-0 group-hover:opacity-100 transition-opacity">
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={onAddChild}
                    className="h-6 w-6 p-0"
                    title="Add child item"
                  >
                    <i className="ri-add-line text-xs"></i>
                  </Button>
                  
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="ghost" size="sm" className="h-6 w-6 p-0">
                        <i className="ri-more-line text-xs"></i>
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent>
                      <DropdownMenuItem onClick={() => onAddNote(item.id, { type: 'text' })}>
                        Add Text Note
                      </DropdownMenuItem>
                      <DropdownMenuItem onClick={() => onAddNote(item.id, { type: 'image' })}>
                        Add Image Note
                      </DropdownMenuItem>
                      <DropdownMenuItem onClick={() => onAddNote(item.id, { type: 'video' })}>
                        Add Video Note
                      </DropdownMenuItem>
                      <DropdownMenuItem onClick={() => onAddNote(item.id, { type: 'audio' })}>
                        Add Audio Note
                      </DropdownMenuItem>
                      <DropdownMenuItem onClick={() => onDeleteOutlineItemWithNotes(item.id)}>
                        Delete Item
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                </div>
              )}
            </div>

            {/* Notes section */}
            {ownLinkedNotes.length > 0 && (
              <Collapsible open={isExpanded} onOpenChange={setIsExpanded}>
                <CollapsibleTrigger className="flex items-center gap-1 text-xs text-muted-foreground hover:text-foreground transition-colors">
                  <i className={`ri-arrow-${isExpanded ? 'down' : 'right'}-s-line`}></i>
                  {ownLinkedNotes.length} note{ownLinkedNotes.length !== 1 ? 's' : ''}
                </CollapsibleTrigger>
                <CollapsibleContent className="mt-2 space-y-2">
                  {ownLinkedNotes.map((note) => (
                    <div key={note.id} className="text-xs p-2 bg-muted rounded border-l-2 border-primary/20">
                      <div className="font-medium">{note.title}</div>
                      {note.content && (
                        <div className="text-muted-foreground mt-1 line-clamp-2">
                          {note.content.substring(0, 100)}...
                        </div>
                      )}
                    </div>
                  ))}
                </CollapsibleContent>
              </Collapsible>
            )}
          </div>
        </div>

        {/* Render children */}
        {item.children && item.children.length > 0 && (
          <div className="mt-2">
            {item.children.map((child, childIndex) => (
              <SimpleOutlineItem
                key={child.id}
                item={child}
                index={childIndex}
                onUpdate={onUpdate}
                onAddChild={onAddChild}
                depth={depth + 1}
                allNotes={allNotes}
                onAddNote={onAddNote}
                onLinkNoteToOutlineItem={onLinkNoteToOutlineItem}
                onDeleteOutlineItemWithNotes={onDeleteOutlineItemWithNotes}
                onNoteDelete={onNoteDelete}
                onNoteDuplicate={onNoteDuplicate}
                onNoteUpdate={onNoteUpdate}
                onFileUpload={onFileUpload}
                allOutlineItems={allOutlineItems}
                onDragStart={onDragStart}
                onDragOver={onDragOver}
                onDrop={onDrop}
                onDragEnd={onDragEnd}
                isDraggedOver={isDraggedOver}
                dragOverPosition={dragOverPosition}
              />
            ))}
          </div>
        )}
      </div>

      {/* After drop zone */}
      <div
        className={`h-2 mt-0 rounded-full mb-1 mx-2 flex items-center justify-center transition-all ${
          isDraggedOver && dragOverPosition === 'after' 
            ? 'bg-blue-400 h-4' 
            : 'bg-transparent hover:bg-blue-100'
        }`}
        onDragOver={(e) => handleDragOver(e, 'after')}
        onDrop={(e) => handleDrop(e, 'after')}
        onDragLeave={handleDragLeave}
      />
    </>
  );
}
