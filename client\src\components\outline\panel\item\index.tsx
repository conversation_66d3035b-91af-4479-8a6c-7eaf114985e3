import React, { useState, useRef, useEffect, useMemo } from 'react';
import { DraggableNotePreview } from './DraggableNotePreview'; // Import the new component
import { OutlineItem as OutlineItemType, Note } from '@/lib/types';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
// Badge might be unused if not re-added for other purposes
// import { Badge } from '@/components/ui/badge';
import {
  Collapsible,
  CollapsibleTrigger,
  CollapsibleContent,
} from '@/components/ui/collapsible';
import {
  DropdownMenu,
  DropdownMenuTrigger,
  DropdownMenuContent,
  DropdownMenuItem,
} from '@/components/ui/dropdown-menu';
import { nanoid } from 'nanoid';




// generateSnippet function removed from here, it's now in DraggableNotePreview.tsx

interface OutlineItemProps {
  item: OutlineItemType;
  index: number;
  onUpdate: (item: OutlineItemType) => void;
  onAddChild: () => void;
  depth: number;
  isOverlay?: boolean;
  activeDragId?: string | null;
  hoveredItemPath?: string[];
  onHover?: (itemId: string) => void;
  allNotes: Note[];
  onAddNote: (outlineItemId: string, noteType: 'text' | 'image' | 'video' | 'audio' | 'file') => void;
  // onOpenNoteEditor: (note: Note) => void; // Removed
  onLinkNoteToOutlineItem: (noteId: string, outlineItemId: string) => void;
  onDeleteOutlineItemWithNotes: (outlineItemId: string) => void;
  onNoteDelete: (noteId: string) => void;
  onNoteDuplicate: (noteId: string) => void;
  isGhost?: boolean;
  // New props for note editing within DraggableNotePreview
  onNoteUpdate: (updatedNote: Note, forceSaveNow?: boolean) => void;
  onFileUpload: (file: File) => Promise<string>; // Corrected signature
  onPopOutMedia?: (mediaUrl: string, mediaType: 'audio' | 'video', title?: string) => void;
  allOutlineItems: OutlineItemType[]; // Flat list of all outline items
  isCurrentSearchResult?: boolean; // For highlighting current search result
  searchTerm?: string; // For highlighting search term within the item
  currentSearchResultMatchDetails?: { // Added from panel
    type: 'outlineTitle' | 'noteTitle' | 'noteContent';
    noteId?: string;
  };
  // Drag and drop props
  onDragStart?: (item: OutlineItemType) => void;
  onDragOver?: (targetId: string, position: 'before' | 'after' | 'child') => void;
  onDrop?: (draggedId: string, targetId: string, position: 'before' | 'after' | 'child') => void;
  onDragEnd?: () => void;
  isDraggedOver?: boolean;
  dragOverPosition?: 'before' | 'after' | 'child';
  dragOverTarget?: {id: string, position: 'before' | 'after' | 'child'} | null;
  // Add sibling functionality
  onAddSibling?: (targetId: string, position: 'before' | 'after') => void;
  // Note drag and drop props
  onNoteDragStart?: (note: Note) => void;
  onNoteDragOver?: (targetType: 'note' | 'outline-item', targetId: string, position?: 'before' | 'after') => void;
  onNoteDrop?: (draggedNoteId: string, targetType: 'note' | 'outline-item', targetId: string, position?: 'before' | 'after') => void;
  onNoteDragEnd?: () => void;
  noteDragOverTarget?: {type: 'note' | 'outline-item'; id: string; position?: 'before' | 'after'} | null;
  draggedNote?: Note | null;
  currentUserPermissionLevel?: 'view' | 'edit' | null;
  isDocumentOwner?: boolean;
}

export function OutlineItem({
  item,
  index,
  onUpdate,
  onAddChild,
  depth = 0,
  isOverlay = false,
  activeDragId = null,
  hoveredItemPath = [],
  onHover,
  allNotes, // Changed from linkedNotes
  onAddNote,
  // onOpenNoteEditor, // Removed
  onLinkNoteToOutlineItem,
  onDeleteOutlineItemWithNotes,
  onNoteDelete,
  onNoteDuplicate,
  isGhost = false,
  // New props
  onNoteUpdate,
  onFileUpload,
  onPopOutMedia,
  allOutlineItems,
  isCurrentSearchResult,
  searchTerm,
  currentSearchResultMatchDetails, // Added from panel
  // Drag and drop props
  onDragStart,
  onDragOver,
  onDrop,
  onDragEnd,
  isDraggedOver,
  dragOverPosition,
  dragOverTarget,
  onAddSibling,
  // Note drag and drop props
  onNoteDragStart,
  onNoteDragOver,
  onNoteDrop,
  onNoteDragEnd,
  noteDragOverTarget,
  draggedNote,
  currentUserPermissionLevel,
  isDocumentOwner,
}: OutlineItemProps) {
  const isReadOnly = currentUserPermissionLevel === 'view';
  const ownLinkedNotes = (allNotes || [])
    .filter(note => note.linkedOutlineId === item.id)
    .sort((a, b) => (a.position || 0) - (b.position || 0));
  const itemRef = useRef<HTMLDivElement>(null); // Ref for the main item div for scrolling
  const [isHovered, setIsHovered] = useState(false);
  const [isLoading, setIsLoading] = useState(false);

  const [isNotesPreviewOpen, setIsNotesPreviewOpen] = useState(false);
  const [hoveredDropZone, setHoveredDropZone] = useState<'before' | 'after' | null>(null);



  // Scroll into view if this item is the current search result
  useEffect(() => {
    if (isCurrentSearchResult && itemRef.current) {
      itemRef.current.scrollIntoView({ behavior: 'smooth', block: 'center' });
    }
  }, [isCurrentSearchResult]);

  // Highlight search term in title - now highlights all matches, not just current result
  const highlightedTitle = useMemo(() => {
    if (!searchTerm || !item.title) {
      return item.title;
    }
    const searchPattern = typeof searchTerm === 'string' ? searchTerm.replace(/[.*+?^${}()|[\]\\]/g, '\\$&') : '';
    if (!searchPattern) return item.title;
    const parts = item.title.split(new RegExp(`(${searchPattern})`, 'gi'));
    return parts.map((part, index) =>
      part.toLowerCase() === searchTerm.toLowerCase() ? (
        <mark key={index} className="bg-yellow-300 dark:bg-yellow-500 px-0.5 py-0 rounded text-black">
          {part}
        </mark>
      ) : (
        part
      )
    );
  }, [searchTerm, item.title]);

  const [isFocused, setIsFocused] = useState(false);

  const handleFocus = () => setIsFocused(true);
  const handleBlur = () => setIsFocused(false);
  

  // Simple drag and drop handlers
  const handleDragStart = (e: React.DragEvent) => {
    e.dataTransfer.setData('text/plain', item.id);
    e.dataTransfer.effectAllowed = 'move';

    // Create a custom drag image (ghost)
    if (itemRef.current) {
      const dragImage = itemRef.current.cloneNode(true) as HTMLElement;
      dragImage.style.position = 'absolute';
      dragImage.style.top = '-1000px';
      dragImage.style.left = '-1000px';
      dragImage.style.width = itemRef.current.offsetWidth + 'px';
      dragImage.style.opacity = '0.8';
      dragImage.style.backgroundColor = 'rgba(59, 130, 246, 0.1)';
      dragImage.style.border = '2px solid rgba(59, 130, 246, 0.5)';
      dragImage.style.borderRadius = '4px';
      dragImage.style.pointerEvents = 'none';
      document.body.appendChild(dragImage);

      e.dataTransfer.setDragImage(dragImage, e.nativeEvent.offsetX, e.nativeEvent.offsetY);

      // Clean up the drag image after a short delay
      setTimeout(() => {
        if (document.body.contains(dragImage)) {
          document.body.removeChild(dragImage);
        }
      }, 0);
    }

    if (onDragStart) onDragStart(item);
  };

  const handleDragOver = (e: React.DragEvent, position: 'before' | 'after' | 'child') => {
    e.preventDefault();
    e.stopPropagation();
    e.dataTransfer.dropEffect = 'move';



    if (onDragOver) onDragOver(item.id, position);
  };

  const handleDrop = (e: React.DragEvent, position: 'before' | 'after' | 'child') => {
    e.preventDefault();
    e.stopPropagation();
    const draggedItemId = e.dataTransfer.getData('text/plain');

    // Clear drag over state immediately
    if (onDragOver) onDragOver('', 'before');


    if (onDrop && draggedItemId !== item.id) {
      onDrop(draggedItemId, item.id, position);
    }
  };

  const handleDragLeave = (e: React.DragEvent) => {
    // Only clear drag state if we're actually leaving this element (not entering a child)
    const relatedTarget = e.relatedTarget as Node;
    if (!relatedTarget || !e.currentTarget.contains(relatedTarget)) {
      if (onDragOver) onDragOver('', 'before'); // Clear drag over state
    }
  };

  const handleDragEnd = () => {
    if (onDragEnd) onDragEnd();
  };

  const style = {
    opacity: isGhost ? 0.6 : 1,
    position: 'relative' as 'relative',
    border: isGhost ? '2px dashed #90cdf4' : undefined,
    backgroundColor: isGhost
      ? 'rgba(144, 205, 244, 0.05)'
      : depth === 0
        ? (index % 2 === 0 ? 'var(--outline-item-background-even)' : 'var(--outline-item-background-odd)')
        : undefined,
    pointerEvents: isGhost ? 'none' : 'auto' as 'auto',
  };
  
  const handleTitleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    onUpdate({ ...item, title: e.target.value });
  };
  


  const handleItemMouseLeave = () => {
    setIsHovered(false);
  };
  
  return (
    <>
      {/* Before drop zone with add button */}
      <div
        className={`h-3 -mt-1 rounded-full mb-1 mx-2 flex items-center justify-center transition-all cursor-pointer ${
          isDraggedOver && dragOverPosition === 'before'
            ? 'bg-blue-500 h-6 border-2 border-blue-600'
            : hoveredDropZone === 'before'
              ? 'bg-blue-100 h-5'
              : 'bg-transparent'
        }`}
        onDragOver={(e) => {
          // Only handle outline item drags, not note drags
          if (!draggedNote) {
            handleDragOver(e, 'before');
          }
        }}
        onDrop={(e) => {
          // Only handle outline item drops, not note drops
          if (!draggedNote) {
            handleDrop(e, 'before');
          }
        }}
        onDragLeave={(e) => handleDragLeave(e)}
        onMouseEnter={() => setHoveredDropZone('before')}
        onMouseLeave={() => setHoveredDropZone(null)}
        onClick={(e) => {
          e.stopPropagation();
          if (onAddSibling) onAddSibling(item.id, 'before');
        }}
        title="Add item before"
      >
        {isDraggedOver && dragOverPosition === 'before' ? (
          <span className="text-xs text-white font-bold px-2">⬆️ DROP BEFORE</span>
        ) : (
          <span className={`text-xs font-bold transition-opacity ${
            hoveredDropZone === 'before'
              ? 'text-blue-500 opacity-100'
              : 'text-neutral-400 opacity-0'
          }`}>+</span>
        )}
      </div>

      <div
        ref={itemRef}
        style={style}
        data-id={item.id}
        className={`outline-item group mb-0.5 py-1 px-1 ${isOverlay ? 'bg-popover shadow-lg border border-primary' : ''} ${isDraggedOver && dragOverPosition === 'child' ? 'outline outline-2 outline-primary' : ''} ${hoveredItemPath?.includes(item.id) ? 'highlighted-path' : ''} ${isCurrentSearchResult ? 'bg-blue-100 dark:bg-blue-900 border-blue-500 ring-1 ring-blue-500' : ''} transition-all max-w-full min-w-0 overflow-visible`}
        onMouseEnter={() => { setIsHovered(true); if (onHover) onHover(item.id); }}
        onMouseLeave={handleItemMouseLeave}
      >


        <div className="flex items-start gap-2 w-full relative">
          <div
            className="drag-handle p-1 text-neutral-300 group-hover:text-neutral-500 cursor-grab active:cursor-grabbing hover:text-neutral-600 hover:bg-neutral-100 rounded transition-colors mt-0.5 relative flex-shrink-0"
            style={{ zIndex: 20 }}
            draggable={true}
            onDragStart={handleDragStart}
            onDragEnd={handleDragEnd}
            title="Drag to reorder"
          >
            <i className="ri-drag-move-fill text-sm"></i>
          </div>
          <div className="flex-1 min-w-0 max-w-[calc(100%-30px)] pr-2 relative">
            <div
              className={`flex items-center mb-0.5 gap-2 w-full min-w-0 pr-1 relative ${
                noteDragOverTarget?.type === 'outline-item' && noteDragOverTarget.id === item.id
                  ? 'bg-blue-100 border-2 border-blue-400 border-dashed rounded-md p-1'
                  : ''
              }`}
              onDragOver={(e) => {
                // Handle outline item drags
                if (!draggedNote) {
                  handleDragOver(e, 'child');
                }
                // Handle note drops on outline items
                if (draggedNote) {
                  e.preventDefault();
                  e.stopPropagation();
                  if (onNoteDragOver) onNoteDragOver('outline-item', item.id);
                }
              }}
              onDrop={(e) => {
                // Handle outline item drops
                if (!draggedNote) {
                  handleDrop(e, 'child');
                }
                // Handle note drops on outline items
                if (draggedNote) {
                  e.preventDefault();
                  e.stopPropagation();
                  const draggedNoteId = e.dataTransfer.getData('application/json');
                  if (draggedNoteId && onNoteDrop) {
                    onNoteDrop(draggedNoteId, 'outline-item', item.id);
                  }
                }
              }}
              onDragLeave={(e) => {
                handleDragLeave(e);
                // Clear note drag over state
                if (draggedNote && onNoteDragOver) {
                  const relatedTarget = e.relatedTarget as Node;
                  if (!relatedTarget || !e.currentTarget.contains(relatedTarget)) {
                    onNoteDragOver('outline-item', '');
                  }
                }
              }}
            >
              <span className="outline-item-number font-mono text-neutral-500 min-w-fit pr-2 flex-shrink-0 text-xs">{item.number}</span>
              <div className="flex-1 min-w-0 overflow-hidden">
                <div className={`flex items-center space-x-2`}>
                  {searchTerm && !isFocused ? (
                    <div className={`outline-item-title w-full bg-transparent border-none p-0 focus:ring-0 focus:outline-none overflow-hidden text-wrap min-w-0 text-sm ${depth === 0 ? 'font-medium' : ''} ${isCurrentSearchResult ? 'font-bold' : ''}`}>
                      {highlightedTitle}
                    </div>
                  ) : (
                    <Input type="text" value={item.title} onChange={handleTitleChange} onFocus={handleFocus} onBlur={handleBlur} className={`outline-item-title w-full bg-transparent border-none p-0 focus:ring-0 focus:outline-none overflow-hidden text-wrap min-w-0 text-sm ${depth === 0 ? 'font-medium' : ''} ${isLoading ? 'opacity-70' : ''} ${isCurrentSearchResult && currentSearchResultMatchDetails?.type === 'outlineTitle' ? 'font-bold' : ''} ${isReadOnly ? 'cursor-default' : ''}`} disabled={isLoading || isReadOnly} readOnly={isReadOnly} />
                  )}

                </div>
              </div>
              {isHovered && !isOverlay && !isReadOnly && (
                <div className="action-buttons flex items-center gap-0.5 flex-shrink-0 ml-auto z-20">
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild><Button variant="ghost" size="icon" className="h-5 w-5 text-neutral-400 hover:text-primary-500" title="Add new note" onClick={(e) => e.stopPropagation()}><i className="ri-sticky-note-add-line text-xs"></i></Button></DropdownMenuTrigger>
                    <DropdownMenuContent onClick={(e) => e.stopPropagation()} side="bottom" align="end">
                      <DropdownMenuItem onSelect={() => onAddNote(item.id, 'text')}><i className="ri-file-text-line mr-2 h-4 w-4" /> Text Note</DropdownMenuItem>
                      <DropdownMenuItem onSelect={() => onAddNote(item.id, 'image')}><i className="ri-image-line mr-2 h-4 w-4" /> Image Note</DropdownMenuItem>
                      <DropdownMenuItem onSelect={() => onAddNote(item.id, 'video')}><i className="ri-video-line mr-2 h-4 w-4" /> Video Note</DropdownMenuItem>
                      <DropdownMenuItem onSelect={() => onAddNote(item.id, 'audio')}><i className="ri-music-line mr-2 h-4 w-4" /> Audio Note</DropdownMenuItem>
                      <DropdownMenuItem onSelect={() => onAddNote(item.id, 'file')}><i className="ri-file-line mr-2 h-4 w-4" /> File Note</DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                  <Button variant="ghost" size="icon" className="h-5 w-5 text-neutral-400 hover:text-primary-500" onClick={() => { const updatedItem = { ...item, children: [...(item.children || []), { id: nanoid(), title: "New Section", number: "", linkedNotes: [] }] }; onUpdate(updatedItem); }} title="Add Child Outline Item"><i className="ri-add-line text-xs"></i></Button>
                  <Button variant="ghost" size="icon" className="h-5 w-5 text-neutral-400 hover:text-danger-500" onClick={() => onDeleteOutlineItemWithNotes(item.id)} title="Delete Outline Item"><i className="ri-delete-bin-line text-xs"></i></Button>
                </div>
              )}
            </div>

            {ownLinkedNotes && (
            <div className="mt-1 pl-[calc(theme(fontSize.xs)+theme(spacing.2))]">
              <Collapsible open={isNotesPreviewOpen} onOpenChange={setIsNotesPreviewOpen} className="outline-item-notes-preview-wrapper">
                <CollapsibleTrigger asChild>
                  {ownLinkedNotes.length > 0 ? (
                    <Button variant="ghost" size="xs" className="text-xs h-6 px-1.5 py-0.5 text-muted-foreground hover:text-foreground data-[state=open]:text-foreground">
                      <i className={`ri-arrow-right-s-line mr-1 transition-transform duration-200 ${isNotesPreviewOpen ? 'rotate-90' : ''}`} />
                      {ownLinkedNotes.length} {ownLinkedNotes.length === 1 ? 'Note' : 'Notes'}
                    </Button>
                  ) : ( <span className="text-xs text-muted-foreground italic pl-1">No notes yet.</span> )}
                </CollapsibleTrigger>
                <CollapsibleContent className="pl-1 mt-1 space-y-1.5">
                  {ownLinkedNotes.map(note => (
                    <DraggableNotePreview
                      key={`note-${note.id}-${item.id}`} // More specific key to prevent duplicates
                      note={note}
                      originalOutlineItemId={item.id}
                      // onOpenNoteEditor={onOpenNoteEditor} // Removed
                      onNoteDelete={onNoteDelete}
                      onNoteDuplicate={onNoteDuplicate}
                      // New props for inline editing
                      onNoteUpdate={onNoteUpdate}
                      onFileUpload={onFileUpload} // Pass directly
                      onPopOutMedia={onPopOutMedia}
                      allOutlineItems={allOutlineItems}
                      // Props for highlighting within the note preview
                      searchTerm={searchTerm}
                      highlightTarget={
                        isCurrentSearchResult && currentSearchResultMatchDetails?.noteId === note.id
                          ? (currentSearchResultMatchDetails.type === 'noteTitle' ? 'title' : (currentSearchResultMatchDetails.type === 'noteContent' ? 'content' : null))
                          : null
                      }
                      // Note drag and drop props
                      onNoteDragStart={onNoteDragStart}
                      onNoteDragOver={onNoteDragOver}
                      onNoteDrop={onNoteDrop}
                      onNoteDragEnd={onNoteDragEnd}
                      noteDragOverTarget={noteDragOverTarget}
                      draggedNote={draggedNote}
                      // Permission props
                      currentUserPermissionLevel={currentUserPermissionLevel}
                      isDocumentOwner={isDocumentOwner}
                    />
                  ))}
                </CollapsibleContent>
              </Collapsible>
            </div>
            )}
            
            {!isOverlay && item.children && item.children.length > 0 && (
              <div className="ml-6 pl-2 border-l border-neutral-200 w-[calc(100%-0.5rem)] overflow-visible mt-1" data-child-container="true">
                {item.children.map((child, childIndex) => (
                  <OutlineItem
                    key={child.id} item={child} index={childIndex} activeDragId={activeDragId} onDeleteOutlineItemWithNotes={onDeleteOutlineItemWithNotes}
                    onUpdate={(updatedChild) => { const newChildren = [...item.children!]; newChildren[childIndex] = updatedChild; onUpdate({ ...item, children: newChildren }); }}
                    onDelete={() => { const newChildren = item.children!.filter((_, i) => i !== childIndex); onUpdate({ ...item, children: newChildren }); }} // This onDelete is for child item structure, not the main item's delete.
                    onAddChild={() => { const updatedChild = { ...child, children: [...(child.children || []), { id: nanoid(), title: "New Section", number: "", linkedNotes: [] }] }; const newChildren = [...item.children!]; newChildren[childIndex] = updatedChild; onUpdate({ ...item, children: newChildren }); }}
                    depth={depth + 1} hoveredItemPath={hoveredItemPath} onHover={onHover}
                    allNotes={allNotes}
                    onAddNote={onAddNote}
                    // onOpenNoteEditor={onOpenNoteEditor} // Removed
                    onLinkNoteToOutlineItem={onLinkNoteToOutlineItem}
                    onNoteDelete={onNoteDelete}
                    onNoteDuplicate={onNoteDuplicate}
                    // Pass down new props to children
                    onNoteUpdate={onNoteUpdate}
                    onFileUpload={onFileUpload} // Pass directly
                    // DRAG AND DROP PROPS - Pass down to children
                    onDragStart={onDragStart}
                    onDragOver={onDragOver}
                    onDrop={onDrop}
                    onDragEnd={onDragEnd}
                    isDraggedOver={dragOverTarget?.id === child.id}
                    dragOverPosition={dragOverTarget?.id === child.id ? dragOverTarget.position : undefined}
                    dragOverTarget={dragOverTarget}
                    onAddSibling={onAddSibling}
                    allOutlineItems={allOutlineItems}
                    // Note drag and drop props - Pass down to children
                    onNoteDragStart={onNoteDragStart}
                    onNoteDragOver={onNoteDragOver}
                    onNoteDrop={onNoteDrop}
                    onNoteDragEnd={onNoteDragEnd}
                    noteDragOverTarget={noteDragOverTarget}
                    draggedNote={draggedNote}
                    // Permission props
                    currentUserPermissionLevel={currentUserPermissionLevel}
                    isDocumentOwner={isDocumentOwner}
                  />
                ))}
              </div>
            )}
          </div>
        </div>
      </div>

      {/* After drop zone with add button */}
      <div
        className={`h-3 mt-0 rounded-full mb-1 mx-2 flex items-center justify-center transition-all cursor-pointer ${
          isDraggedOver && dragOverPosition === 'after'
            ? 'bg-blue-500 h-6 border-2 border-blue-600'
            : hoveredDropZone === 'after'
              ? 'bg-blue-100 h-5'
              : 'bg-transparent'
        }`}
        onDragOver={(e) => {
          // Only handle outline item drags, not note drags
          if (!draggedNote) {
            handleDragOver(e, 'after');
          }
        }}
        onDrop={(e) => {
          // Only handle outline item drops, not note drops
          if (!draggedNote) {
            handleDrop(e, 'after');
          }
        }}
        onDragLeave={(e) => handleDragLeave(e)}
        onMouseEnter={() => setHoveredDropZone('after')}
        onMouseLeave={() => setHoveredDropZone(null)}
        onClick={(e) => {
          e.stopPropagation();
          if (onAddSibling) onAddSibling(item.id, 'after');
        }}
        title="Add item after"
      >
        {isDraggedOver && dragOverPosition === 'after' ? (
          <span className="text-xs text-white font-bold px-2">⬇️ DROP AFTER</span>
        ) : (
          <span className={`text-xs font-bold transition-opacity ${
            hoveredDropZone === 'after'
              ? 'text-blue-500 opacity-100'
              : 'text-neutral-400 opacity-0'
          }`}>+</span>
        )}
      </div>
    </>
  );
}