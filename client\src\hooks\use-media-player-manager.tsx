import { useState, useCallback } from 'react';

export interface MediaPlayerInfo {
  id: string;
  url: string;
  type: 'audio' | 'video';
  title?: string;
  window?: Window | null;
}

export function useMediaPlayerManager() {
  const [poppedOutPlayers, setPoppedOutPlayers] = useState<MediaPlayerInfo[]>([]);

  const popOutMediaPlayer = useCallback((url: string, type: 'audio' | 'video', title?: string) => {
    // Create a unique ID for this media player
    const playerId = `${type}-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
    
    // Encode the URL and title for safe URL transmission
    const encodedUrl = encodeURIComponent(url);
    const encodedTitle = title ? encodeURIComponent(title) : '';
    
    // Create the popout URL
    const popoutUrl = `/popout/media/${encodedUrl}/${type}${encodedTitle ? `/${encodedTitle}` : ''}`;
    
    // Open the new window
    const newWindow = window.open(
      popoutUrl, 
      `media-player-${playerId}`, 
      'width=800,height=600,resizable=yes,scrollbars=no,status=no,menubar=no,toolbar=no'
    );

    if (newWindow) {
      const playerInfo: MediaPlayerInfo = {
        id: playerId,
        url,
        type,
        title,
        window: newWindow
      };

      setPoppedOutPlayers(prev => [...prev, playerInfo]);

      // Monitor when the window is closed
      const checkClosed = setInterval(() => {
        if (newWindow.closed) {
          clearInterval(checkClosed);
          setPoppedOutPlayers(prev => prev.filter(player => player.id !== playerId));
        }
      }, 500);

      return playerId;
    }

    return null;
  }, []);

  const closeMediaPlayer = useCallback((playerId: string) => {
    setPoppedOutPlayers(prev => {
      const player = prev.find(p => p.id === playerId);
      if (player?.window && !player.window.closed) {
        player.window.close();
      }
      return prev.filter(p => p.id !== playerId);
    });
  }, []);

  const closeAllMediaPlayers = useCallback(() => {
    poppedOutPlayers.forEach(player => {
      if (player.window && !player.window.closed) {
        player.window.close();
      }
    });
    setPoppedOutPlayers([]);
  }, [poppedOutPlayers]);

  const isMediaPlayerOpen = useCallback((url: string) => {
    return poppedOutPlayers.some(player => player.url === url && player.window && !player.window.closed);
  }, [poppedOutPlayers]);

  const getOpenMediaPlayer = useCallback((url: string) => {
    return poppedOutPlayers.find(player => player.url === url && player.window && !player.window.closed);
  }, [poppedOutPlayers]);

  const focusMediaPlayer = useCallback((playerId: string) => {
    const player = poppedOutPlayers.find(p => p.id === playerId);
    if (player?.window && !player.window.closed) {
      player.window.focus();
      return true;
    }
    return false;
  }, [poppedOutPlayers]);

  return {
    poppedOutPlayers,
    popOutMediaPlayer,
    closeMediaPlayer,
    closeAllMediaPlayers,
    isMediaPlayerOpen,
    getOpenMediaPlayer,
    focusMediaPlayer
  };
}
