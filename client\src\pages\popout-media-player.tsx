import React, { useEffect, useState } from 'react';
import { useParams } from 'wouter';
import { Button } from '@/components/ui/button';
import { MediaPlayer } from '@/components/ui/media-player';
import { Loader2 } from 'lucide-react';

export function PopoutMediaPlayerPage() {
  const { mediaUrl, mediaType, title } = useParams<{ 
    mediaUrl: string; 
    mediaType: string; 
    title?: string; 
  }>();
  
  const [decodedUrl, setDecodedUrl] = useState<string>('');
  const [decodedTitle, setDecodedTitle] = useState<string>('');
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    // Decode the URL-encoded parameters
    if (mediaUrl) {
      try {
        const decoded = decodeURIComponent(mediaUrl);
        setDecodedUrl(decoded);
      } catch (error) {
        console.error('Failed to decode media URL:', error);
        setDecodedUrl(mediaUrl);
      }
    }

    if (title) {
      try {
        const decoded = decodeURIComponent(title);
        setDecodedTitle(decoded);
      } catch (error) {
        console.error('Failed to decode title:', error);
        setDecodedTitle(title);
      }
    }

    setIsLoading(false);
  }, [mediaUrl, title]);

  const handlePopIn = () => {
    window.close();
  };

  if (isLoading) {
    return (
      <div className="h-screen w-full flex items-center justify-center bg-background">
        <div className="flex flex-col items-center gap-4">
          <Loader2 className="h-16 w-16 animate-spin text-primary" />
          <p className="text-lg font-medium text-foreground">Loading media player...</p>
        </div>
      </div>
    );
  }

  if (!decodedUrl || !mediaType) {
    return (
      <div className="h-screen w-full flex items-center justify-center bg-background">
        <div className="text-center">
          <h3 className="text-lg font-medium text-foreground">Invalid Media</h3>
          <p className="text-sm text-muted-foreground mt-1">
            The media URL or type is missing or invalid.
          </p>
          <Button onClick={handlePopIn} className="mt-4">
            Close Window
          </Button>
        </div>
      </div>
    );
  }

  if (mediaType !== 'audio' && mediaType !== 'video') {
    return (
      <div className="h-screen w-full flex items-center justify-center bg-background">
        <div className="text-center">
          <h3 className="text-lg font-medium text-foreground">Unsupported Media Type</h3>
          <p className="text-sm text-muted-foreground mt-1">
            Only audio and video files can be played in the popout player.
          </p>
          <Button onClick={handlePopIn} className="mt-4">
            Close Window
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="h-screen w-full flex flex-col bg-background">
      {/* Header with title and controls */}
      <div className="p-4 bg-background border-b border-border flex items-center justify-between">
        <div className="flex items-center gap-2">
          <i className={`ri-${mediaType === 'video' ? 'video' : 'music'}-line text-lg text-primary`}></i>
          <h1 className="font-medium text-foreground">
            {decodedTitle || `${mediaType === 'video' ? 'Video' : 'Audio'} Player`}
          </h1>
        </div>
        <Button
          variant="ghost"
          size="sm"
          onClick={handlePopIn}
          title="Close and return to main window"
        >
          <i className="ri-close-line text-sm mr-1"></i>
          Close
        </Button>
      </div>

      {/* Media player */}
      <div className="flex-1 p-4 flex items-center justify-center">
        <div className="w-full max-w-4xl">
          <MediaPlayer
            src={decodedUrl}
            type={mediaType as 'audio' | 'video'}
            title={decodedTitle}
            isPoppedOut={true}
            onPopOut={handlePopIn}
            className="w-full"
          />
        </div>
      </div>

      {/* Footer with additional info */}
      <div className="p-2 bg-muted/30 border-t border-border text-center">
        <p className="text-xs text-muted-foreground">
          Popout Media Player • Use the controls above to manage playback
        </p>
      </div>
    </div>
  );
}
