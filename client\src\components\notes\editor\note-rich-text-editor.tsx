import React, { useRef, useState } from 'react';
import { AiEnhancedRichTextEditor } from '@/components/ui/ai-enhanced-rich-text-editor';
import { Button } from '@/components/ui/button';

interface NoteRichTextEditorProps {
  value: string;
  onChange: (value: string) => void;
  className?: string;
  onShowSubscriptionModal?: () => void;
  onImageUploadClick?: () => void;
}

// This component wraps AiEnhancedRichTextEditor and adds a sticky formatting toolbar
export function NoteRichTextEditor({
  value,
  onChange,
  className,
  onShowSubscriptionModal,
  onImageUploadClick
}: NoteRichTextEditorProps) {
  const editorContainerRef = useRef<HTMLDivElement>(null);
  
  // Apply formatting command to the current selection
  const applyFormat = (command: string, value: string = '') => {
    // Focus the editor to ensure commands are applied to it
    if (editorContainerRef.current) {
      const editorDiv = editorContainerRef.current.querySelector('[contenteditable="true"]');
      if (editorDiv) {
        (editorDiv as HTMLElement).focus();
        document.execCommand(command, false, value);
      }
    }
  };

  return (
    <div className="relative flex flex-col" ref={editorContainerRef}>
      {/* Sticky formatting toolbar */}
      <div className="sticky top-0 z-10 bg-background/95 backdrop-blur-sm border-b border-neutral-200 py-1 px-2 flex items-center gap-1 mb-1">
        <Button 
          variant="ghost" 
          size="sm" 
          className="h-7 w-7 p-0" 
          onClick={() => applyFormat('bold')}
          title="Bold"
        >
          <i className="ri-bold text-foreground"></i>
        </Button>
        <Button 
          variant="ghost" 
          size="sm" 
          className="h-7 w-7 p-0" 
          onClick={() => applyFormat('italic')}
          title="Italic"
        >
          <i className="ri-italic text-foreground"></i>
        </Button>
        <Button 
          variant="ghost" 
          size="sm" 
          className="h-7 w-7 p-0" 
          onClick={() => applyFormat('underline')}
          title="Underline"
        >
          <i className="ri-underline text-foreground"></i>
        </Button>
        <div className="h-4 w-px bg-neutral-200 mx-1"></div>
        <Button 
          variant="ghost" 
          size="sm" 
          className="h-7 w-7 p-0" 
          onClick={() => applyFormat('formatBlock', '<h2>')}
          title="Heading"
        >
          <i className="ri-heading text-foreground"></i>
        </Button>
        <Button 
          variant="ghost" 
          size="sm" 
          className="h-7 w-7 p-0" 
          onClick={() => applyFormat('insertUnorderedList')}
          title="Bullet List"
        >
          <i className="ri-list-unordered text-foreground"></i>
        </Button>
        <Button 
          variant="ghost" 
          size="sm" 
          className="h-7 w-7 p-0" 
          onClick={() => applyFormat('insertOrderedList')}
          title="Numbered List"
        >
          <i className="ri-list-ordered text-foreground"></i>
        </Button>
        <div className="h-4 w-px bg-neutral-200 mx-1"></div>
        <Button 
          variant="ghost" 
          size="sm" 
          className="h-7 w-7 p-0" 
          onClick={onImageUploadClick}
          title="Insert Image"
        >
          <i className="ri-image-add-line text-foreground"></i>
        </Button>
      </div>
      
      {/* AI-enhanced editor */}
      <AiEnhancedRichTextEditor
        value={value}
        onChange={onChange}
        className={className}
        onShowSubscriptionModal={onShowSubscriptionModal}
      />
    </div>
  );
}