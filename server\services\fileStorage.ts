import { S3Client, PutObjectCommand, DeleteObjectCommand, HeadObjectCommand } from '@aws-sdk/client-s3';
import { nanoid } from 'nanoid';

export interface UploadResult {
  url: string;
  id: string;
  type: string;
  name: string;
  displayName: string;
  size: number;
  mimetype: string;
}

export class FileStorageService {
  private s3Client: S3Client;
  private bucketName: string;
  private cdnEndpoint: string;

  constructor() {
    // Validate required environment variables
    const endpoint = process.env.DO_SPACES_ENDPOINT;
    const region = process.env.DO_SPACES_REGION;
    const accessKeyId = process.env.DO_SPACES_ACCESS_KEY;
    const secretAccessKey = process.env.DO_SPACES_SECRET_KEY;
    const bucketName = process.env.DO_SPACES_BUCKET;
    const cdnEndpoint = process.env.DO_SPACES_CDN_ENDPOINT;

    if (!endpoint || !region || !accessKeyId || !secretAccessKey || !bucketName || !cdnEndpoint) {
      throw new Error('Missing required DigitalOcean Spaces environment variables');
    }

    this.s3Client = new S3Client({
      endpoint,
      region,
      credentials: {
        accessKeyId,
        secretAccessKey,
      },
      // Force path-style URLs for DigitalOcean Spaces compatibility
      forcePathStyle: false,
    });

    this.bucketName = bucketName;
    this.cdnEndpoint = cdnEndpoint;
  }

  /**
   * Upload a file to DigitalOcean Spaces
   */
  async uploadFile(
    fileBuffer: Buffer,
    originalName: string,
    mimeType: string,
    size: number
  ): Promise<UploadResult> {
    try {
      // Debug logging
      console.log('FileStorage.uploadFile called with:', {
        originalName,
        mimeType,
        size,
        bufferLength: fileBuffer.length,
        isBuffer: Buffer.isBuffer(fileBuffer)
      });

      // Validate input
      if (!Buffer.isBuffer(fileBuffer) || fileBuffer.length === 0) {
        throw new Error('Invalid file buffer provided');
      }

      // Generate unique file ID and safe name
      const fileId = nanoid();
      const fileExt = originalName.split('.').pop()?.toLowerCase() || '';
      const safeName = fileExt ? `${fileId}.${fileExt}` : fileId;

      // Upload to DigitalOcean Spaces
      const uploadCommand = new PutObjectCommand({
        Bucket: this.bucketName,
        Key: safeName,
        Body: fileBuffer,
        ContentType: mimeType,
        // Make the file publicly readable
        ACL: 'public-read',
        // Set cache control for better performance
        CacheControl: 'max-age=31536000', // 1 year
      });

      await this.s3Client.send(uploadCommand);

      // Determine file type for client-side processing
      let fileType = 'file';
      if (mimeType.startsWith('image/')) {
        fileType = 'image';
      } else if (mimeType.startsWith('video/')) {
        fileType = 'video';
      } else if (mimeType.startsWith('audio/')) {
        fileType = 'audio';
      }

      // Return the CDN URL for faster access
      const fileUrl = `${this.cdnEndpoint}/${safeName}`;

      return {
        url: fileUrl,
        id: fileId,
        type: fileType,
        name: originalName,
        displayName: originalName,
        size,
        mimetype: mimeType,
      };
    } catch (error) {
      console.error('Error uploading file to DigitalOcean Spaces:', error);
      throw new Error('Failed to upload file to cloud storage');
    }
  }

  /**
   * Delete a file from DigitalOcean Spaces using the full filename
   */
  async deleteFile(fileName: string): Promise<void> {
    try {
      const deleteCommand = new DeleteObjectCommand({
        Bucket: this.bucketName,
        Key: fileName,
      });

      await this.s3Client.send(deleteCommand);
      console.log(`Successfully deleted file: ${fileName}`);
    } catch (error) {
      console.warn(`Could not delete file: ${fileName}`, error);
      // Don't throw an error here - the file might already be deleted or not exist
    }
  }

  /**
   * Get the public URL for a file
   */
  getFileUrl(fileName: string): string {
    return `${this.cdnEndpoint}/${fileName}`;
  }
}

// Export a singleton instance
export const fileStorage = new FileStorageService();
