import { OutlineItem } from '@/lib/types';
import { nanoid } from 'nanoid';

/**
 * Calculates outline numbers for a hierarchy of outline items.
 * Assigns numbers like 1, 1.1, 1.1.1, etc. with support for deep nesting.
 */
export function calculateOutlineNumbers(outline: OutlineItem[]): OutlineItem[] {
  const assignNumbers = (items: OutlineItem[], prefix = ''): OutlineItem[] => {
    return items.map((item, index) => {
      // Format: For top level: 1, 2, 3...
      // For nested levels: 1.1, 1.2, 2.1, 2.1.1, etc.
      const number = prefix ? `${prefix}.${index + 1}` : `${index + 1}`;

      // Create a deep copy to avoid mutating the original
      let result: OutlineItem = {
        ...item,
        number,
        children: item.children ? [...item.children] : []
      };

      // Process children recursively if they exist
      if (result.children && result.children.length > 0) {
        result.children = assignNumbers(result.children, number);
      }

      return result;
    });
  };

  // Handle empty outlines
  if (!outline || outline.length === 0) {
    return [];
  }

  return assignNumbers(outline);
}

/**
 * Creates a new outline item
 */
export function createOutlineItem(title = 'New Item', children?: OutlineItem[]): OutlineItem {
  return {
    id: nanoid(),
    number: '', // Will be assigned by calculateOutlineNumbers
    title,
    children,
    linkedNoteCount: 0
  };
}

/**
 * Flattens a nested outline structure into a single array
 */
export function flattenOutline(outline: OutlineItem[]): OutlineItem[] {
  let result: OutlineItem[] = [];
  
  for (const item of outline) {
    result.push(item);
    
    if (item.children && item.children.length > 0) {
      result = result.concat(flattenOutline(item.children));
    }
  }
  
  return result;
}

/**
 * Finds an outline item by ID in a nested structure
 */
export function findItemById(items: OutlineItem[], id: string): OutlineItem | null {
  for (const item of items) {
    if (item.id === id) return item;
    
    if (item.children && item.children.length > 0) {
      const found = findItemById(item.children, id);
      if (found) return found;
    }
  }
  
  return null;
}

/**
 * Counts all outline items including children
 */
export function countOutlineItems(outline: OutlineItem[]): number {
  let count = outline.length;
  
  for (const item of outline) {
    if (item.children && item.children.length > 0) {
      count += countOutlineItems(item.children);
    }
  }
  
  return count;
}