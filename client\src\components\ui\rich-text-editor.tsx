
import React, { useRef, useState, useEffect, useCallback } from "react";
import { cn } from "@/lib/utils";
import { Button } from "./button";
import { Citation } from "@/lib/types";
import { usePreferences } from "@/hooks/use-preferences";



// Helper function to find sentence boundaries in text
function findSentenceBoundaries(text: string): { start: number; end: number }[] {
  const sentences: { start: number; end: number }[] = [];

  // More comprehensive regex for sentence endings
  // Matches periods, exclamation marks, question marks followed by:
  // - Whitespace and capital letter
  // - End of string
  // - Newline characters
  const sentenceEndRegex = /[.!?]+(?=\s+[A-Z]|\s*$|\s*\n)/g;

  // Common abbreviations that shouldn't end sentences
  const abbreviations = /\b(?:Dr|Mr|Mrs|Ms|Prof|Sr|Jr|vs|etc|i\.e|e\.g|Inc|Corp|Ltd|St|Ave|Blvd|Co|Corp|Inc|LLC|Ltd|No|Vol|Ch|Fig|Ref|pp|p)\./gi;

  let lastEnd = 0;
  let match;

  // Find all potential sentence endings
  const potentialEndings: number[] = [];
  while ((match = sentenceEndRegex.exec(text)) !== null) {
    potentialEndings.push(match.index + match[0].length);
  }

  // Filter out abbreviations
  const realEndings = potentialEndings.filter(ending => {
    const beforeEnding = text.substring(Math.max(0, ending - 15), ending);
    return !abbreviations.test(beforeEnding);
  });

  // Also look for line breaks as potential sentence boundaries
  // This helps with content that has short lines like "Line 1 of additional content"
  const lineBreaks = [];
  for (let i = 0; i < text.length; i++) {
    if (text[i] === '\n') {
      // Check if the line before this break looks like a complete thought
      const lineStart = text.lastIndexOf('\n', i - 1) + 1;
      const lineContent = text.substring(lineStart, i).trim();

      // If the line has substantial content and doesn't end with punctuation,
      // treat the line break as a sentence boundary
      if (lineContent.length > 10 && !/[.!?]$/.test(lineContent)) {
        lineBreaks.push(i);
      }
    }
  }

  // Combine real endings and line breaks, then sort
  const allEndings = [...realEndings, ...lineBreaks].sort((a, b) => a - b);

  // Create sentence objects
  allEndings.forEach(ending => {
    if (lastEnd < ending) {
      // Skip whitespace at the beginning
      let start = lastEnd;
      while (start < ending && /\s/.test(text[start])) {
        start++;
      }

      if (start < ending) {
        sentences.push({
          start: start,
          end: ending
        });
      }
      lastEnd = ending;
    }
  });

  // Add the last sentence if there's remaining text
  if (lastEnd < text.length) {
    let start = lastEnd;
    while (start < text.length && /\s/.test(text[start])) {
      start++;
    }

    if (start < text.length) {
      sentences.push({
        start: start,
        end: text.length
      });
    }
  }

  return sentences;
}

// Helper function to find the word containing a specific position in the editor
function findWordAtPosition(
  editorElement: HTMLElement,
  x: number,
  y: number
): { range: Range; wordText: string } | null {
  try {
    // Create a range from the point
    const pointRange = document.caretRangeFromPoint(x, y);
    if (!pointRange) {
      return null;
    }

    // Get all text content from the editor
    const walker = document.createTreeWalker(
      editorElement,
      NodeFilter.SHOW_TEXT,
      null,
    );

    let textContent = "";
    let currentNode;
    while ((currentNode = walker.nextNode())) {
      textContent += currentNode.textContent || "";
    }

    if (!textContent.trim()) {
      return null;
    }

    // Reset walker to calculate position
    walker.currentNode = editorElement;

    let absolutePosition = 0;
    let targetPosition = 0;

    // Find the absolute position of the range start
    while ((currentNode = walker.nextNode())) {
      if (currentNode === pointRange.startContainer) {
        targetPosition = absolutePosition + pointRange.startOffset;
        break;
      }
      absolutePosition += currentNode.textContent?.length || 0;
    }

    // Find word boundaries around the target position
    // Use more precise word boundary detection that respects punctuation and spaces
    const beforeText = textContent.substring(0, targetPosition);
    const afterText = textContent.substring(targetPosition);

    // Find the start of the current word (look backwards for word boundary)
    // Match word characters (letters, numbers, apostrophes) but stop at spaces or punctuation
    const wordStartMatch = beforeText.match(/[a-zA-Z0-9']*$/);
    const wordStart = wordStartMatch ? targetPosition - wordStartMatch[0].length : targetPosition;

    // Find the end of the current word (look forwards for word boundary)
    // Match word characters but stop at spaces, punctuation, or other non-word characters
    const wordEndMatch = afterText.match(/^[a-zA-Z0-9']*/);
    const wordEnd = wordEndMatch ? targetPosition + wordEndMatch[0].length : targetPosition;

    // Extract the word text
    const wordText = textContent.substring(wordStart, wordEnd).trim();

    // Don't highlight if it's just whitespace, empty, or too short
    if (!wordText || wordText.length < 1) {
      return null;
    }

    // Create a range for the word
    const wordRange = document.createRange();
    let currentPos = 0;
    let startNode: Node | null = null;
    let startOffset = 0;
    let endNode: Node | null = null;
    let endOffset = 0;

    // Reset walker to find the nodes for word start and end
    walker.currentNode = editorElement;
    while ((currentNode = walker.nextNode())) {
      const nodeLength = currentNode.textContent?.length || 0;

      // Check if word start is in this node
      if (!startNode && currentPos + nodeLength > wordStart) {
        startNode = currentNode;
        startOffset = wordStart - currentPos;
      }

      // Check if word end is in this node
      if (!endNode && currentPos + nodeLength >= wordEnd) {
        endNode = currentNode;
        endOffset = wordEnd - currentPos;
        break;
      }

      currentPos += nodeLength;
    }

    if (!startNode || !endNode) {
      return null;
    }

    try {
      wordRange.setStart(startNode, startOffset);
      wordRange.setEnd(endNode, endOffset);
      return { range: wordRange, wordText };
    } catch (error) {
      console.error("Error creating word range:", error);
      return null;
    }
  } catch (error) {
    console.error("Error finding word at position:", error);
    return null;
  }
}

// Helper function to find the exact character position in a text node based on mouse coordinates
function findCharacterOffsetInTextNode(
  textNode: Text,
  mouseX: number,
  mouseY: number,
): number {
  const text = textNode.textContent || "";
  if (text.length === 0) return 0;

  // Create a temporary range to measure text
  const range = document.createRange();
  range.setStart(textNode, 0);

  // Binary search to find the closest character position
  let left = 0;
  let right = text.length;
  let bestOffset = 0;
  let bestDistance = Infinity;

  while (left <= right) {
    const mid = Math.floor((left + right) / 2);
    range.setEnd(textNode, mid);

    const rect = range.getBoundingClientRect();

    // Calculate distance from mouse to the end of this text segment
    const distance = Math.abs(mouseX - rect.right) + Math.abs(mouseY - (rect.top + rect.height / 2));

    if (distance < bestDistance) {
      bestDistance = distance;
      bestOffset = mid;
    }

    // Decide which direction to search
    if (rect.right < mouseX) {
      left = mid + 1;
    } else {
      right = mid - 1;
    }
  }

  // Also check the position just after the best offset
  if (bestOffset < text.length) {
    range.setEnd(textNode, bestOffset + 1);
    const rect = range.getBoundingClientRect();
    const distance = Math.abs(mouseX - rect.right) + Math.abs(mouseY - (rect.top + rect.height / 2));

    if (distance < bestDistance) {
      bestOffset = bestOffset + 1;
    }
  }

  return bestOffset;
}

// Helper function to position cursor more precisely at a specific point in the editor
function positionCursorFromPoint(
  x: number,
  y: number,
  editorElement: HTMLElement,
): Range | null {
  let range: Range | null = null;

  try {
    // First try using the native caretRangeFromPoint method if available
    // This gives the most precise cursor positioning at exact coordinates
    const docAny = document as any;
    if (typeof docAny.caretRangeFromPoint === "function") {
      range = docAny.caretRangeFromPoint(x, y);

      // Verify the range is inside our editor
      if (range && !editorElement.contains(range.commonAncestorContainer)) {
        console.log(
          "Range from caretRangeFromPoint is outside editor, trying alternative",
        );
        range = null;
      } else if (range) {
        console.log("Successfully positioned cursor using caretRangeFromPoint");
        return range;
      }
    }

    // Second approach: Try to find the deepest text node at the position and measure precisely
    const elements = document.elementsFromPoint(x, y);

    // First look for a text node at or near that position
    for (const element of elements) {
      if (editorElement.contains(element)) {
        // Function to find the deepest text node in an element
        const findDeepestTextNode = (node: Node): Node => {
          if (node.nodeType === Node.TEXT_NODE) {
            return node;
          }

          // If it's an element with children, recurse into its children
          if (node.hasChildNodes()) {
            for (const child of Array.from(node.childNodes)) {
              const textNode = findDeepestTextNode(child);
              if (textNode && textNode.nodeType === Node.TEXT_NODE) {
                return textNode;
              }
            }
          }

          // No text node found, return the node itself
          return node;
        };

        // Try to find a text node within this element
        const textNode = findDeepestTextNode(element);

        // Create a range at this text node
        range = document.createRange();

        if (textNode.nodeType === Node.TEXT_NODE) {
          // Calculate the precise character position based on mouse coordinates
          const offset = findCharacterOffsetInTextNode(textNode as Text, x, y);
          range.setStart(textNode, offset);
          range.collapse(true); // Collapse to start position
        } else {
          // If not a text node, position at the end of the node
          range.selectNodeContents(textNode);
          range.collapse(false); // Position at end
        }

        console.log("Positioned cursor at text node with precise measurement");
        return range;
      }
    }

    // Third approach: Just use the first element we found inside the editor
    for (const element of elements) {
      if (editorElement.contains(element)) {
        range = document.createRange();
        range.selectNodeContents(element);
        range.collapse(false); // position at end
        console.log("Positioned cursor at element end");
        return range;
      }
    }

    // Last resort - place cursor at the end of the editor
    range = document.createRange();
    range.selectNodeContents(editorElement);
    range.collapse(false); // position at end
    console.log("Positioned cursor at editor end");
  } catch (e) {
    console.error("Error positioning cursor:", e);

    // Fallback to end of editor
    try {
      range = document.createRange();
      range.selectNodeContents(editorElement);
      range.collapse(false);
    } catch (err) {
      console.error("Fallback positioning failed:", err);
      return null;
    }
  }

  return range;
}

interface RichTextEditorProps {
  value: string;
  onChange: (value: string) => void;
  minHeight?: string;
  maxHeight?: string;
  onBlur?: () => void;
  placeholder?: string;
  className?: string;
  fillContainer?: boolean;
  readOnly?: boolean;
}

export function RichTextEditor({
  value,
  onChange,
  minHeight = "300px",
  maxHeight,
  onBlur,
  placeholder,
  className,
  fillContainer = false,
  readOnly = false,
}: RichTextEditorProps) {
  const editorRef = useRef<HTMLDivElement>(null);
  const [internalValue, setInternalValue] = useState(value);
  const isUserTyping = useRef(false);
  const [isDraggingOver, setIsDraggingOver] = useState(false);

  const [highlightedWord, setHighlightedWord] = useState<{
    range: Range;
    wordText: string;
  } | null>(null);

  // Get paragraph formatting preferences
  const { preferences } = usePreferences();

  // Function to wrap content in paragraphs when setting content
  const wrapContentInParagraphs = (content: string): string => {
    if (!content || content.trim() === '' || content === '<br>') {
      return '';
    }

    // Create a temporary container to parse the content
    const tempDiv = document.createElement('div');
    tempDiv.innerHTML = content;

    // Check if content is already properly wrapped
    const childNodes = Array.from(tempDiv.childNodes);
    let needsWrapping = false;

    for (const node of childNodes) {
      if (node.nodeType === Node.TEXT_NODE && node.textContent?.trim()) {
        needsWrapping = true;
        break;
      }
      if (node.nodeType === Node.ELEMENT_NODE) {
        const element = node as Element;
        const tagName = element.tagName.toLowerCase();
        if (!['p', 'div', 'h1', 'h2', 'h3', 'h4', 'h5', 'h6', 'blockquote', 'ul', 'ol', 'li'].includes(tagName)) {
          needsWrapping = true;
          break;
        }
      }
    }

    if (!needsWrapping) {
      return content;
    }

    // Wrap content in paragraphs
    const fragment = document.createDocumentFragment();
    let currentParagraph: HTMLParagraphElement | null = null;

    for (const node of childNodes) {
      if (node.nodeType === Node.TEXT_NODE) {
        const text = node.textContent || '';
        if (text.trim()) {
          if (!currentParagraph) {
            currentParagraph = document.createElement('p');
            fragment.appendChild(currentParagraph);
          }
          currentParagraph.appendChild(node.cloneNode(true));
        }
      } else if (node.nodeType === Node.ELEMENT_NODE) {
        const element = node as Element;
        const tagName = element.tagName.toLowerCase();

        if (['p', 'div', 'h1', 'h2', 'h3', 'h4', 'h5', 'h6', 'blockquote', 'ul', 'ol', 'li'].includes(tagName)) {
          fragment.appendChild(node.cloneNode(true));
          currentParagraph = null;
        } else {
          if (!currentParagraph) {
            currentParagraph = document.createElement('p');
            fragment.appendChild(currentParagraph);
          }
          currentParagraph.appendChild(node.cloneNode(true));
        }
      }
    }

    // Get the wrapped content
    const wrappedDiv = document.createElement('div');
    wrappedDiv.appendChild(fragment);
    return wrappedDiv.innerHTML;
  };

  // Set initial content on mount
  useEffect(() => {
    if (editorRef.current) {
      const wrappedContent = wrapContentInParagraphs(value || "");
      editorRef.current.innerHTML = wrappedContent;
    }
  }, []);

  // Apply paragraph formatting styles
  useEffect(() => {
    if (editorRef.current) {
      const editor = editorRef.current;

      // Create or update the style element for paragraph formatting
      let styleElement = document.getElementById('paragraph-formatting-styles') as HTMLStyleElement;
      if (!styleElement) {
        styleElement = document.createElement('style');
        styleElement.id = 'paragraph-formatting-styles';
        document.head.appendChild(styleElement);
      }

      // Apply paragraph formatting CSS
      const css = `
        [contenteditable="true"] p,
        [contenteditable="true"] div {
          margin-bottom: ${preferences.paragraphSpacing || "0.5em"} !important;
          margin-top: 0 !important;
        }
        [contenteditable="true"] p:last-child,
        [contenteditable="true"] div:last-child {
          margin-bottom: 0 !important;
        }
      `;

      styleElement.textContent = css;
    }
  }, [preferences.paragraphSpacing]);

  // Sync editor content with props when changed externally
  useEffect(() => {
    if (!isUserTyping.current && editorRef.current && value !== internalValue) {
      const wrappedContent = wrapContentInParagraphs(value || "");
      editorRef.current.innerHTML = wrappedContent;
      setInternalValue(value);
    }
  }, [value, internalValue]);

  // Function to clear all formatting and reset to normal text
  const clearFormatting = () => {
    if (editorRef.current) {
      // Remove all formatting from the current selection
      document.execCommand('removeFormat', false, '');

      // Also ensure we're not in superscript mode
      document.execCommand('superscript', false, '');

      // Force normal font size
      document.execCommand('fontSize', false, '3'); // Normal size
    }
  };

  // Debounce timer for paragraph wrapping to prevent excessive calls
  const paragraphWrappingTimerRef = useRef<NodeJS.Timeout | null>(null);

  // Function to ensure text content is wrapped in paragraph elements
  const ensureParagraphWrapping = useCallback(() => {
    if (!editorRef.current) return;

    // Clear any existing timer
    if (paragraphWrappingTimerRef.current) {
      clearTimeout(paragraphWrappingTimerRef.current);
    }

    // Debounce the wrapping to prevent excessive calls
    paragraphWrappingTimerRef.current = setTimeout(() => {
      if (!editorRef.current) return;

      const editor = editorRef.current;
      const selection = window.getSelection();
      let savedRange: Range | null = null;

      // Save current selection
      if (selection && selection.rangeCount > 0) {
        savedRange = selection.getRangeAt(0).cloneRange();
      }

      // Check if we have any direct text nodes that need wrapping
      const childNodes = Array.from(editor.childNodes);
      let needsWrapping = false;

      for (const node of childNodes) {
        // Check for text nodes or inline elements that should be in paragraphs
        if (node.nodeType === Node.TEXT_NODE && node.textContent?.trim()) {
          needsWrapping = true;
          break;
        }
        // Check for inline elements that aren't in block elements
        if (node.nodeType === Node.ELEMENT_NODE) {
          const element = node as Element;
          const tagName = element.tagName.toLowerCase();
          // If it's an inline element (not a block element), it should be wrapped
          if (!['p', 'div', 'h1', 'h2', 'h3', 'h4', 'h5', 'h6', 'blockquote', 'ul', 'ol', 'li'].includes(tagName)) {
            needsWrapping = true;
            break;
          }
        }
      }

      if (needsWrapping) {
        // Create a document fragment to hold the new structure
        const fragment = document.createDocumentFragment();
        let currentParagraph: HTMLParagraphElement | null = null;

        for (const node of childNodes) {
          if (node.nodeType === Node.TEXT_NODE) {
            const text = node.textContent || '';
            if (text.trim()) {
              // Create a new paragraph if we don't have one
              if (!currentParagraph) {
                currentParagraph = document.createElement('p');
                fragment.appendChild(currentParagraph);
              }
              currentParagraph.appendChild(node.cloneNode(true));
            }
          } else if (node.nodeType === Node.ELEMENT_NODE) {
            const element = node as Element;
            const tagName = element.tagName.toLowerCase();

            if (['p', 'div', 'h1', 'h2', 'h3', 'h4', 'h5', 'h6', 'blockquote', 'ul', 'ol', 'li'].includes(tagName)) {
              // This is already a block element, add it directly
              fragment.appendChild(node.cloneNode(true));
              currentParagraph = null; // Reset current paragraph
            } else {
              // This is an inline element, add it to current paragraph
              if (!currentParagraph) {
                currentParagraph = document.createElement('p');
                fragment.appendChild(currentParagraph);
              }
              currentParagraph.appendChild(node.cloneNode(true));
            }
          }
        }

        // Replace editor content with wrapped content
        editor.innerHTML = '';
        editor.appendChild(fragment);

        // Restore selection if possible
        if (savedRange && selection) {
          try {
            selection.removeAllRanges();
            selection.addRange(savedRange);
          } catch (e) {
            // If we can't restore the exact selection, place cursor at the end
            const range = document.createRange();
            range.selectNodeContents(editor);
            range.collapse(false);
            selection.removeAllRanges();
            selection.addRange(range);
          }
        }
      }
    }, 50); // 50ms debounce to prevent excessive calls
  }, []);

  // Simple content change handler that doesn't mess with selection
  const handleInput = () => {
    if (editorRef.current && !readOnly) {
      isUserTyping.current = true;

      // Get content before any modifications
      const originalContent = editorRef.current.innerHTML;

      // Check if editor is empty or only contains whitespace/br tags
      const textContent = editorRef.current.textContent || '';
      const htmlContent = editorRef.current.innerHTML;

      if (!textContent.trim() && (htmlContent === '' || htmlContent === '<br>' || htmlContent === '<div><br></div>')) {
        // Editor is empty, create a paragraph for proper indentation
        const paragraph = document.createElement('p');
        paragraph.innerHTML = '<br>'; // Add a br to make the paragraph visible
        editorRef.current.innerHTML = '';
        editorRef.current.appendChild(paragraph);

        // Place cursor in the paragraph
        const selection = window.getSelection();
        if (selection) {
          const range = document.createRange();
          range.setStart(paragraph, 0);
          range.collapse(true);
          selection.removeAllRanges();
          selection.addRange(range);
        }
      } else {
        // Ensure content is properly wrapped in paragraphs
        ensureParagraphWrapping();
      }

      const newContent = editorRef.current.innerHTML;

      // Only trigger onChange if content actually changed to prevent unnecessary saves
      if (newContent !== originalContent) {
        // Check if the content is very short and might be new typing after clearing
        // If so, clear any inherited formatting
        if (textContent.length <= 10 && newContent.includes('<font size="1">')) {
          console.log('Detected superscript formatting on new text, clearing formatting');
          clearFormatting();
          // Re-get content after clearing formatting
          const clearedContent = editorRef.current.innerHTML;
          setInternalValue(clearedContent);
          onChange(clearedContent);
        } else {
          setInternalValue(newContent);
          onChange(newContent);
        }
      }

      // Allow external updates after a short delay
      setTimeout(() => {
        isUserTyping.current = false;
      }, 100);
    }
  };

  // Handle key events to clear formatting when appropriate and ensure proper paragraph structure
  const handleKeyDown = (e: React.KeyboardEvent<HTMLDivElement>) => {
    if (!editorRef.current || readOnly) return;

    // Handle Tab key for manual indentation
    if (e.key === 'Tab') {
      e.preventDefault();
      if (e.shiftKey) {
        handleUnindent();
      } else {
        handleIndent();
      }
      return;
    }

    // Handle Enter key to ensure proper paragraph creation
    if (e.key === 'Enter' && !e.shiftKey) {
      const selection = window.getSelection();
      if (selection && selection.rangeCount > 0) {
        const range = selection.getRangeAt(0);
        const container = range.commonAncestorContainer;

        // Find the current block element
        let blockElement = container.nodeType === Node.TEXT_NODE ? container.parentElement : container as Element;
        while (blockElement && blockElement !== editorRef.current) {
          const tagName = blockElement.tagName.toLowerCase();
          if (['p', 'div', 'h1', 'h2', 'h3', 'h4', 'h5', 'h6'].includes(tagName)) {
            break;
          }
          blockElement = blockElement.parentElement;
        }

        // If we're not in a block element, let the default behavior happen and then wrap
        if (!blockElement || blockElement === editorRef.current) {
          // Let the default Enter behavior happen, then wrap content
          setTimeout(() => {
            ensureParagraphWrapping();
          }, 10);
        }
      }
    }

    // If the user is typing normal characters and we detect superscript formatting, clear it
    if (!e.ctrlKey && !e.altKey && !e.metaKey) {
      const selection = window.getSelection();
      if (selection && selection.rangeCount > 0) {
        const range = selection.getRangeAt(0);
        const container = range.commonAncestorContainer;

        // Check if we're inside a font tag with size="1" (superscript)
        let element = container.nodeType === Node.TEXT_NODE ? container.parentElement : container as Element;
        while (element && element !== editorRef.current) {
          if (element.tagName === 'FONT' && element.getAttribute('size') === '1') {
            console.log('Detected typing in superscript context, clearing formatting');
            // Clear formatting after a short delay to let the character be typed first
            setTimeout(() => {
              clearFormatting();
            }, 10);
            break;
          }
          element = element.parentElement;
        }
      }
    }
  };

  // Handle common commands
  const execCommand = (command: string, value: string = "") => {
    document.execCommand(command, false, value);
    if (editorRef.current) {
      const newContent = editorRef.current.innerHTML;
      setInternalValue(newContent);
      onChange(newContent);
    }
  };

  // Handle manual indentation
  const handleIndent = () => {
    if (!editorRef.current) return;

    const selection = window.getSelection();
    if (!selection || selection.rangeCount === 0) return;

    // Find the paragraph containing the current selection
    let currentNode = selection.anchorNode;
    let paragraph = null;

    // Traverse up the DOM tree to find the paragraph element
    while (currentNode && currentNode !== editorRef.current) {
      if (currentNode.nodeType === Node.ELEMENT_NODE &&
          (currentNode as Element).tagName.toLowerCase() === 'p') {
        paragraph = currentNode as HTMLParagraphElement;
        break;
      }
      currentNode = currentNode.parentNode;
    }

    if (!paragraph) return;

    // Get current text-indent value or default to 0
    const currentStyle = window.getComputedStyle(paragraph);
    const currentTextIndent = parseInt(currentStyle.textIndent) || 0;

    // Add 40px (equivalent to about 4 spaces) for first-line indentation
    const newTextIndent = currentTextIndent + 40;
    paragraph.style.textIndent = `${newTextIndent}px`;

    // Update content
    const newContent = editorRef.current.innerHTML;
    setInternalValue(newContent);
    onChange(newContent);
  };

  // Handle manual unindentation
  const handleUnindent = () => {
    if (!editorRef.current) return;

    const selection = window.getSelection();
    if (!selection || selection.rangeCount === 0) return;

    // Find the paragraph containing the current selection
    let currentNode = selection.anchorNode;
    let paragraph = null;

    // Traverse up the DOM tree to find the paragraph element
    while (currentNode && currentNode !== editorRef.current) {
      if (currentNode.nodeType === Node.ELEMENT_NODE &&
          (currentNode as Element).tagName.toLowerCase() === 'p') {
        paragraph = currentNode as HTMLParagraphElement;
        break;
      }
      currentNode = currentNode.parentNode;
    }

    if (!paragraph) return;

    // Get current text-indent value or default to 0
    const currentStyle = window.getComputedStyle(paragraph);
    const currentTextIndent = parseInt(currentStyle.textIndent) || 0;

    // Remove 40px for unindentation, but don't go below 0
    const newTextIndent = Math.max(0, currentTextIndent - 40);

    if (newTextIndent === 0) {
      // Remove the text-indent style entirely if it's 0
      paragraph.style.removeProperty('text-indent');
    } else {
      paragraph.style.textIndent = `${newTextIndent}px`;
    }

    // Update content
    const newContent = editorRef.current.innerHTML;
    setInternalValue(newContent);
    onChange(newContent);
  };

  // Handle drag over event
  const handleDragOver = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    e.stopPropagation();

    // Check if the dragged data is a citation
    if (e.dataTransfer.types.includes("application/json")) {
      setIsDraggingOver(true);

      // Calculate precise drop position for visual indicator
      const editorElement = editorRef.current;
      if (editorElement) {
        // Get mouse coordinates relative to the page
        const dropX = e.clientX;
        const dropY = e.clientY;

        // Find the word at the current mouse position
        const wordInfo = findWordAtPosition(editorElement, dropX, dropY);

        if (wordInfo) {
          // Highlight the word being hovered over
          setHighlightedWord(wordInfo);
        } else {
          // Fallback to original behavior if no word found
          setHighlightedWord(null);
        }
      }
    }
  };

  // Handle drag leave event
  const handleDragLeave = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDraggingOver(false);
    setHighlightedWord(null);
  };

  // Handle drop event
  const handleDrop = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDraggingOver(false);

    try {
      // First focus the editor to ensure it's ready to receive content
      if (editorRef.current) {
        editorRef.current.focus();
      }

      // Check if the dropped data is a citation
      if (e.dataTransfer.types.includes("application/json")) {
        const citationData = e.dataTransfer.getData("application/json");
        const citation: Citation = JSON.parse(citationData);

        // Validate the citation object first
        if (!citation || typeof citation !== "object") {
          throw new Error("Invalid citation data format");
        }

        // Check if citation data has a specific format (footnote or endnote)
        const hasFootnote =
          typeof citation.isFootnote === "boolean"
            ? citation.isFootnote
            : false;

        let formattedCitation = "";

        // Get the editor element
        const editorElement = editorRef.current;
        if (!editorElement) {
          console.error("Editor element not found");
          return;
        }

        // Try to get current selection or create one at the drop point
        const selection = window.getSelection();
        let range: Range | null = null;

        // Check if we have a highlighted word to place the citation at its end
        if (highlightedWord) {
          console.log("Placing citation at end of highlighted word:", highlightedWord.wordText.trim());

          // Create a range at the end of the highlighted word
          range = highlightedWord.range.cloneRange();
          range.collapse(false); // Collapse to end of word

          // Clear the word highlighting
          setHighlightedWord(null);

          // Set the selection to this range
          selection?.removeAllRanges();
          selection?.addRange(range);
        } else {
          // Fallback to original behavior
          try {
            // Check if we have a valid selection range
            if (selection && selection.rangeCount > 0) {
              range = selection.getRangeAt(0);

              // Verify the range is within our editor
              if (!editorElement.contains(range.commonAncestorContainer)) {
                throw new Error("Selection is outside editor");
              }
            } else {
              throw new Error("No selection range");
            }
          } catch (error) {
            console.log(
              "Selection not found, setting cursor position at drop point",
            );

            // Get coordinates from the original drag event
            const dropX = e.clientX;
            const dropY = e.clientY;

            // Create a new range at the drop position
            range = positionCursorFromPoint(dropX, dropY, editorElement);

            if (range) {
              // Set the selection to this range
              selection?.removeAllRanges();
              selection?.addRange(range);
            } else {
              // Fall back to end of editor
              range = document.createRange();
              range.selectNodeContents(editorElement);
              range.collapse(false); // collapse to end
              selection?.removeAllRanges();
              selection?.addRange(range);
            }
          }
        }

        if (!range) {
          console.error("Could not establish a valid insertion point");
          return;
        }

        // Format and insert citation based on type
        if (hasFootnote || (citation.marker && !citation.year)) {
          // Footnote: Create an actual HTML superscript element
          const supElement = document.createElement("sup");
          supElement.textContent = citation.marker;

          // Add data attribute to link back to the reference
          if (citation.id) {
            supElement.setAttribute("data-reference-id", citation.id);
          }

          // Insert the superscript element at the cursor position
          range.deleteContents();
          range.insertNode(supElement);

          // Position cursor after the inserted element
          range.setStartAfter(supElement);
          range.setEndAfter(supElement);
          selection?.removeAllRanges();
          selection?.addRange(range);

          console.log("Inserted footnote citation with DOM element");
        } else {
          // Endnote: Create a text node for the parenthetical citation
          // Extract author or use marker if no clear author available
          let authorText = "Citation";

          if (citation.authors && citation.authors.length > 0) {
            // Use last name of first author if available
            authorText =
              citation.authors[0].split(" ").pop() || citation.marker;
          } else if (
            citation.reference &&
            typeof citation.reference === "string"
          ) {
            // Try to extract author from reference
            const parts = citation.reference.split(".");
            authorText = parts[0] || citation.marker || "Citation";
          } else if (citation.marker) {
            // Use marker as fallback
            authorText = citation.marker;
          }

          // Get year from citation or use current year
          const yearText = citation.year || new Date().getFullYear().toString();

          // Create text node with parenthetical citation
          const textNode = document.createTextNode(
            `(${authorText}, ${yearText})`,
          );

          // Insert the citation at the cursor position
          range.deleteContents();
          range.insertNode(textNode);

          // Position cursor after the inserted citation
          range.setStartAfter(textNode);
          range.setEndAfter(textNode);
          selection?.removeAllRanges();
          selection?.addRange(range);

          console.log("Inserted endnote citation with DOM node");
        }

        // Update the editor content after insertion
        const newContent = editorElement.innerHTML;
        setInternalValue(newContent);
        onChange(newContent);

        // Ensure editor has focus after insertion
        editorElement.focus();

        // Add a log to help debug the citations detection
        console.log(
          "Updated editor content:",
          newContent.substring(0, 100) + "...",
        );
      }
    } catch (error) {
      console.error("Error handling citation drop:", error);
    }
  };

  return (
    <div
      className={cn(
        "flex flex-col h-full bg-background text-foreground relative",
        className,
      )}
    >
      {/* Hide toolbar when in read-only mode */}
      {!readOnly && (
        <div className="sticky top-0 left-0 right-0 z-10 flex p-1 gap-1 bg-background/95 backdrop-blur-sm shadow-sm border-b border-border" style={{ backgroundColor: "hsl(var(--background) / 0.95)" }}>
        <Button
          type="button"
          size="sm"
          variant="ghost"
          className="h-8 w-8 p-0"
          onClick={() => execCommand("bold")}
        >
          <i className="ri-bold text-lg" />
        </Button>
        <Button
          type="button"
          size="sm"
          variant="ghost"
          className="h-8 w-8 p-0"
          onClick={() => execCommand("italic")}
        >
          <i className="ri-italic text-lg" />
        </Button>
        <Button
          type="button"
          size="sm"
          variant="ghost"
          className="h-8 w-8 p-0"
          onClick={() => execCommand("underline")}
        >
          <i className="ri-underline text-lg" />
        </Button>
        <div className="w-px h-8 bg-gray-300 dark:bg-gray-600 mx-1" />
        <Button
          type="button"
          size="sm"
          variant="ghost"
          className="h-8 w-8 p-0"
          onClick={handleIndent}
          title="Indent"
        >
          <i className="ri-indent-increase text-lg" />
        </Button>
        <Button
          type="button"
          size="sm"
          variant="ghost"
          className="h-8 w-8 p-0"
          onClick={handleUnindent}
          title="Unindent"
        >
          <i className="ri-indent-decrease text-lg" />
        </Button>
        <div className="w-px h-8 bg-gray-300 dark:bg-gray-600 mx-1" />
        <Button
          type="button"
          size="sm"
          variant="ghost"
          className="h-8 w-8 p-0"
          onClick={() => execCommand("justifyLeft")}
        >
          <i className="ri-align-left text-lg" />
        </Button>
        <Button
          type="button"
          size="sm"
          variant="ghost"
          className="h-8 w-8 p-0"
          onClick={() => execCommand("justifyCenter")}
        >
          <i className="ri-align-center text-lg" />
        </Button>
        <Button
          type="button"
          size="sm"
          variant="ghost"
          className="h-8 w-8 p-0"
          onClick={() => execCommand("justifyRight")}
        >
          <i className="ri-align-right text-lg" />
        </Button>
        <div className="w-px h-8 bg-gray-300 dark:bg-gray-600 mx-1" />
        <Button
          type="button"
          size="sm"
          variant="ghost"
          className="h-8 w-8 p-0"
          onClick={() => execCommand("insertUnorderedList")}
        >
          <i className="ri-list-unordered text-lg" />
        </Button>
        <Button
          type="button"
          size="sm"
          variant="ghost"
          className="h-8 w-8 p-0"
          onClick={() => execCommand("insertOrderedList")}
        >
          <i className="ri-list-ordered text-lg" />
        </Button>
        </div>
      )}

      {/* Container div - fills available space when fillContainer is true */}
      <div style={{ height: fillContainer ? "100%" : minHeight, position: "relative" }}>
        <div
          ref={editorRef}
          contentEditable={!readOnly}
          className={cn(
            "px-4 pb-12 focus:outline-none prose prose-sm max-w-none border-0 outline-none ring-0 text-foreground",
            !readOnly ? "pt-10" : "pt-4", // Add appropriate top padding when toolbar is visible
            isDraggingOver &&
              "bg-[var(--icon-purple)]/5 border-2 border-dashed border-[var(--icon-purple)]/20",
          )}
          style={{
            minHeight: fillContainer ? "150px" : minHeight, // Use smaller min height when filling container
            height: fillContainer ? "100%" : minHeight, // Fill container or use fixed height
            maxHeight: fillContainer ? undefined : minHeight, // Remove max constraint when filling
            overflowY: "scroll",  // Always show vertical scrollbar on contentEditable
            border: isDraggingOver ? undefined : "none",
            outline: "none",
            boxShadow: "none",
            backgroundColor: "var(--background)",
            // Apply paragraph formatting preferences
            "--line-spacing": preferences.lineSpacing || "1.15",
            "--paragraph-spacing": preferences.paragraphSpacing || "0.5em",
          } as React.CSSProperties & { [key: string]: string }}
          onInput={handleInput}
          onKeyDown={handleKeyDown}
          onBlur={onBlur}
          onDragOver={handleDragOver}
          onDragLeave={handleDragLeave}
          onDrop={handleDrop}
          data-placeholder={placeholder}
        />

        {/* Word highlighting - shows which word will receive the citation */}
        {highlightedWord && (
          <WordHighlight
            range={highlightedWord.range}
            editorElement={editorRef.current}
          />
        )}


      </div>
    </div>
  );
}

// Component to highlight a word during citation drag
function WordHighlight({
  range,
  editorElement
}: {
  range: Range;
  editorElement: HTMLElement | null;
}) {
  const [highlightRects, setHighlightRects] = useState<DOMRect[]>([]);

  useEffect(() => {
    if (!editorElement || !range) return;

    try {
      // Get all the rectangles for the range (handles multi-line words)
      const rects = Array.from(range.getClientRects());

      if (rects.length === 0) {
        setHighlightRects([]);
        return;
      }

      // Get the editor's position relative to the viewport
      const editorRect = editorElement.getBoundingClientRect();

      // Convert to positions relative to the editor element itself
      const relativeRects = rects
        .filter(rect => rect.width > 0 && rect.height > 0) // Filter out empty rects
        .map(rect => ({
          left: rect.left - editorRect.left,
          top: rect.top - editorRect.top,
          width: rect.width,
          height: rect.height,
          right: rect.right - editorRect.left,
          bottom: rect.bottom - editorRect.top,
          x: rect.x - editorRect.left,
          y: rect.y - editorRect.top,
        } as DOMRect));

      setHighlightRects(relativeRects);
    } catch (error) {
      console.error("Error calculating word highlight:", error);
      setHighlightRects([]);
    }
  }, [range, editorElement]);

  if (!editorElement || highlightRects.length === 0) return null;

  return (
    <>
      {highlightRects.map((rect, index) => (
        <div
          key={index}
          className="absolute pointer-events-none z-5"
          style={{
            left: rect.left,
            top: rect.top,
            width: rect.width,
            height: rect.height,
            backgroundColor: "rgba(59, 130, 246, 0.15)", // Blue highlight
            borderRadius: "2px",
            border: "1px solid rgba(59, 130, 246, 0.3)",
          }}
        />
      ))}
    </>
  );
}
