import { Switch, Route, useLocation } from "wouter";
import { Toaster } from "@/components/ui/toaster";
import { TooltipProvider } from "@/components/ui/tooltip";
import { AuthProvider, useAuth } from "@/hooks/use-auth";
import { QueryClientProvider } from "@tanstack/react-query";
import { queryClient } from "@/lib/queryClient";
import { useEffect } from "react";
import { Loader2 } from "lucide-react";
import { PreferencesProvider } from "@/hooks/use-preferences";

// Pages
import NotFound from "@/pages/not-found";
import DocumentsPage from "@/pages/documents-page";
import Document from "@/pages/document";
import LandingPage from "@/pages/landing";
import AuthPage from "@/pages/auth-page";
import { RequestAccessPage } from "@/pages/request-access-page";
import { PopoutPanelPage } from "@/pages/popout-panel";
import { PopoutMediaPlayerPage } from "@/pages/popout-media-player";
import AdminDashboard from "@/pages/admin-dashboard";
import EmergencyAdminPage from "@/pages/emergency-admin";

// Components
import { ProtectedRoute } from "@/components/protected-route";

function Router() {
  const { user, isLoading } = useAuth();

  // Redirect to landing or dashboard based on auth status
  const RootRedirect = () => {
    const [_, navigate] = useLocation();
    
    useEffect(() => {
      if (!isLoading) {
        if (user) {
          navigate("/documents");
        } else {
          navigate("/landing");
        }
      }
    }, [user, isLoading, navigate]);
    
    // Show loading state while authentication is being checked
    if (isLoading) {
      return (
        <div className="flex justify-center items-center min-h-screen">
          <Loader2 className="h-10 w-10 animate-spin text-primary" />
        </div>
      );
    }
    
    return null;
  };

  return (
    <Switch>
      {/* Root redirect */}
      <Route path="/" component={RootRedirect} />
      
      {/* Protected routes - require authentication */}
      <ProtectedRoute path="/documents" component={DocumentsPage} />
      <ProtectedRoute path="/document/:id" component={Document} />
      <ProtectedRoute path="/admin" component={AdminDashboard} />
      
      {/* Public routes */}
      <Route path="/landing" component={LandingPage} />
      <Route path="/auth" component={AuthPage} />
      <Route path="/emergency-admin" component={EmergencyAdminPage} />
      <ProtectedRoute path="/request-access/:documentId" component={RequestAccessPage} />
      <Route path="/popout/:documentId/:panel" component={PopoutPanelPage} />
      <Route path="/popout/media/:mediaUrl/:mediaType/:title?" component={PopoutMediaPlayerPage} />
      <Route component={NotFound} />
    </Switch>
  );
}

function App() {
  return (
    <>
      <Toaster />
      <QueryClientProvider client={queryClient}>
        <AuthProvider>
          <PreferencesProvider>
            <TooltipProvider>
              <Router />
            </TooltipProvider>
          </PreferencesProvider>
        </AuthProvider>
      </QueryClientProvider>
    </>
  );
}

export default App;
